#!/bin/bash
# CIM-Tracker WSL 环境设置脚本
# 用于安装 mamba-ssm 和配置高性能训练环境

set -e  # 遇到错误立即退出

echo "🚀 开始设置 CIM-Tracker WSL 环境..."

# ================== 系统信息检查 ==================
echo "📋 检查系统信息..."
echo "操作系统: $(uname -a)"
echo "Python 版本: $(python3 --version)"
echo "CUDA 版本: $(nvcc --version 2>/dev/null || echo '未安装 CUDA')"

# ================== 检查 GPU ==================
echo "🎮 检查 GPU 可用性..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi
    echo "✅ NVIDIA GPU 可用"
else
    echo "❌ 未检测到 NVIDIA GPU 或驱动"
    echo "请确保已安装 NVIDIA 驱动和 CUDA"
    exit 1
fi

# ================== 创建虚拟环境 ==================
echo "🐍 创建 Python 虚拟环境..."
if [ ! -d "venv_mamba_ssm" ]; then
    python3 -m venv venv_mamba_ssm
    echo "✅ 虚拟环境创建完成"
else
    echo "📝 虚拟环境已存在"
fi

# 激活虚拟环境
source venv_mamba_ssm/bin/activate
echo "✅ 虚拟环境已激活"

# ================== 升级基础工具 ==================
echo "⬆️  升级基础工具..."
pip install --upgrade pip setuptools wheel

# ================== 安装 PyTorch ==================
echo "🔥 安装 PyTorch (CUDA 版本)..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 验证 PyTorch 安装
python3 -c "
import torch
print(f'PyTorch 版本: {torch.__version__}')
print(f'CUDA 可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA 版本: {torch.version.cuda}')
    print(f'GPU 数量: {torch.cuda.device_count()}')
    print(f'GPU 名称: {torch.cuda.get_device_name(0)}')
"

# ================== 安装编译依赖 ==================
echo "🔧 安装编译依赖..."
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    cmake \
    ninja-build \
    git \
    wget \
    curl

# ================== 安装 mamba-ssm ==================
echo "🐍 安装 mamba-ssm..."

# 方法1: 直接从 PyPI 安装
echo "尝试从 PyPI 安装 mamba-ssm..."
if pip install mamba-ssm; then
    echo "✅ mamba-ssm 从 PyPI 安装成功"
else
    echo "❌ PyPI 安装失败，尝试从源码编译..."
    
    # 方法2: 从源码编译
    echo "📦 从源码编译 mamba-ssm..."
    
    # 克隆仓库
    if [ ! -d "mamba" ]; then
        git clone https://github.com/state-spaces/mamba.git
    fi
    
    cd mamba
    
    # 安装依赖
    pip install -r requirements.txt
    pip install -e .
    
    cd ..
    
    echo "✅ mamba-ssm 从源码编译完成"
fi

# ================== 验证 mamba-ssm 安装 ==================
echo "🧪 验证 mamba-ssm 安装..."
python3 -c "
try:
    from mamba_ssm import Mamba
    import torch
    
    # 创建测试实例
    model = Mamba(d_model=256, d_state=16, d_conv=4, expand=2)
    
    # 测试前向传播
    x = torch.randn(2, 10, 256)
    y = model(x)
    
    print('✅ mamba-ssm 安装验证成功')
    print(f'   输入形状: {x.shape}')
    print(f'   输出形状: {y.shape}')
    
except ImportError as e:
    print(f'❌ mamba-ssm 导入失败: {e}')
    exit(1)
except Exception as e:
    print(f'❌ mamba-ssm 测试失败: {e}')
    exit(1)
"

# ================== 安装其他依赖 ==================
echo "📦 安装其他项目依赖..."
pip install \
    numpy \
    opencv-python \
    pillow \
    einops \
    wandb \
    scipy \
    matplotlib \
    tqdm \
    tensorboard

# ================== 安装 CIM-Tracker 依赖 ==================
echo "🎯 安装 CIM-Tracker 特定依赖..."

# 安装 MultiScaleDeformableAttention
echo "安装 MultiScaleDeformableAttention..."
cd models/ops
python3 setup.py build install
cd ../..

echo "✅ MultiScaleDeformableAttention 安装完成"

# ================== 创建数据目录 ==================
echo "📁 创建数据目录结构..."
mkdir -p /data/Datasets
mkdir -p /data/CIM_models
mkdir -p outputs

echo "✅ 数据目录创建完成"

# ================== 性能测试 ==================
echo "⚡ 运行性能测试..."
python3 -c "
import torch
from models.cdiim_mamba_ssm import check_mamba_ssm_compatibility

print('🧪 运行 mamba-ssm 兼容性测试...')
if check_mamba_ssm_compatibility():
    print('✅ 所有测试通过，环境配置成功！')
else:
    print('❌ 测试失败，请检查安装')
    exit(1)
"

# ================== 创建启动脚本 ==================
echo "📝 创建训练启动脚本..."
cat > train_mamba_ssm.sh << 'EOF'
#!/bin/bash
# CIM-Tracker mamba-ssm 训练启动脚本

# 激活虚拟环境
source venv_mamba_ssm/bin/activate

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 启动训练
echo "🚀 启动 CIM-Tracker mamba-ssm 训练..."
python3 main.py configs/cim_tracker_mamba_ssm.args

EOF

chmod +x train_mamba_ssm.sh
echo "✅ 训练脚本创建完成: train_mamba_ssm.sh"

# ================== 创建数据迁移脚本 ==================
echo "📝 创建数据迁移脚本..."
cat > migrate_data_from_windows.sh << 'EOF'
#!/bin/bash
# 从 Windows 迁移数据到 WSL

WINDOWS_DATA_PATH="/mnt/d/Projects/Datasets"
WSL_DATA_PATH="/data/Datasets"

echo "📦 迁移数据从 Windows 到 WSL..."

if [ -d "$WINDOWS_DATA_PATH" ]; then
    echo "发现 Windows 数据路径: $WINDOWS_DATA_PATH"
    
    # 创建符号链接或复制数据
    echo "创建符号链接..."
    ln -sf "$WINDOWS_DATA_PATH/DanceTrack" "$WSL_DATA_PATH/"
    ln -sf "$WINDOWS_DATA_PATH/SportsMOT" "$WSL_DATA_PATH/"
    ln -sf "$WINDOWS_DATA_PATH/BFT" "$WSL_DATA_PATH/"
    ln -sf "$WINDOWS_DATA_PATH/CIM_models" "/data/"
    
    echo "✅ 数据迁移完成"
else
    echo "❌ 未找到 Windows 数据路径"
    echo "请手动复制数据或调整路径"
fi

EOF

chmod +x migrate_data_from_windows.sh
echo "✅ 数据迁移脚本创建完成: migrate_data_from_windows.sh"

# ================== 完成设置 ==================
echo ""
echo "🎉 CIM-Tracker WSL 环境设置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 运行数据迁移: ./migrate_data_from_windows.sh"
echo "2. 启动训练: ./train_mamba_ssm.sh"
echo "3. 监控训练: tail -f outputs/cim_tracker_mamba_ssm/train.log"
echo ""
echo "🚀 预期性能提升："
echo "   - 训练速度: 5-10倍提升"
echo "   - 内存效率: 显著改善"
echo "   - 序列长度: 支持更长序列"
echo ""
echo "💡 提示："
echo "   - 确保 CUDA 驱动正确安装"
echo "   - 监控 GPU 内存使用情况"
echo "   - 根据 GPU 内存调整 batch_size"
