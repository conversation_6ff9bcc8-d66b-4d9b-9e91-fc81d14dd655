#!/usr/bin/env python3
"""
CIM-Tracker 并行化优化性能基准测试
对比串行处理 vs 并行处理的性能差异
"""

import torch
import torch.nn as nn
import time
import numpy as np
from torch.utils.data import DataLoader
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datasets.dance import build as build_dataset
from models import build_model
from util.misc import mot_collate_fn
from main import get_args_parser

def create_benchmark_args():
    """创建基准测试参数"""
    parser = get_args_parser()
    args = parser.parse_args([])
    
    # 基准测试特定配置
    args.meta_arch = 'motr'
    args.dataset_file = 'e2e_dance'
    args.mot_path = 'D:/Projects/Datasets'
    args.det_db = 'D:/Projects/Datasets/CIM_models/det_db_motrv2.json'
    args.sampler_lengths = [4]  # 使用更长的序列测试
    args.mamba_num_layers = 4   # 使用完整的层数
    args.num_queries = 20       # 增加查询数量
    args.batch_size = 2         # 增加批次大小
    args.aux_loss = False
    args.use_checkpoint = False
    args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    args.append_crowd = False
    
    return args

def benchmark_data_loading():
    """基准测试数据加载性能"""
    print("=" * 60)
    print("📊 数据加载性能基准测试")
    print("=" * 60)
    
    args = create_benchmark_args()
    
    # 构建数据集
    dataset = build_dataset(image_set='train', args=args)
    dataloader = DataLoader(
        dataset,
        batch_size=1,  # 使用batch_size=1避免尺寸不匹配问题
        collate_fn=mot_collate_fn,
        num_workers=0,
        shuffle=False
    )
    
    # 预热
    print("🔥 数据加载预热...")
    for i, batch in enumerate(dataloader):
        if i >= 3:  # 预热3个批次
            break
    
    # 基准测试
    print("⏱️  开始数据加载基准测试...")
    start_time = time.time()
    batch_times = []
    
    for i, batch in enumerate(dataloader):
        batch_start = time.time()
        
        # 模拟数据处理
        if batch and 'imgs' in batch:
            imgs = batch['imgs']
            if isinstance(imgs, torch.Tensor):
                _ = imgs.to(args.device)
        
        batch_end = time.time()
        batch_times.append(batch_end - batch_start)
        
        if i >= 20:  # 测试20个批次
            break
    
    total_time = time.time() - start_time
    avg_batch_time = np.mean(batch_times)
    
    print(f"✅ 数据加载基准测试完成")
    print(f"   - 总时间: {total_time:.3f}s")
    print(f"   - 平均每批次: {avg_batch_time:.3f}s")
    print(f"   - 吞吐量: {1 / avg_batch_time:.2f} clips/s")  # 修正为实际的batch_size=1
    
    return avg_batch_time

def benchmark_model_forward():
    """基准测试模型前向传播性能"""
    print("=" * 60)
    print("🚀 模型前向传播性能基准测试")
    print("=" * 60)
    
    args = create_benchmark_args()
    
    # 构建模型
    model, criterion, _ = build_model(args)
    model = model.to(args.device)
    model.eval()  # 使用eval模式避免dropout等随机性
    
    print(f"✅ 模型加载完成")
    print(f"   - 设备: {args.device}")
    print(f"   - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建基准测试数据
    B, T, C, H, W = args.batch_size, args.sampler_lengths[0], 3, 800, 1333
    N_queries = args.num_queries
    
    mock_data = {
        'imgs': torch.randn(B, T, C, H, W, device=args.device),
        'gt_instances': [[
            create_mock_instances(device=args.device) for _ in range(T)
        ] for _ in range(B)],
        'proposals': [[
            torch.randn(5, 5, device=args.device) for _ in range(T)
        ] for _ in range(B)]
    }
    
    print(f"✅ 测试数据创建完成")
    print(f"   - 输入形状: {mock_data['imgs'].shape}")
    print(f"   - 批次大小: {B}, 序列长度: {T}, 查询数: {N_queries}")
    
    # GPU预热
    print("🔥 GPU预热...")
    with torch.no_grad():
        for _ in range(5):
            _ = model(mock_data)
    
    torch.cuda.synchronize()
    
    # 基准测试
    print("⏱️  开始前向传播基准测试...")
    forward_times = []
    memory_usage = []
    
    for i in range(20):
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        
        start_time = time.time()
        
        with torch.no_grad():
            outputs = model(mock_data)
        
        torch.cuda.synchronize()
        end_time = time.time()
        
        forward_time = end_time - start_time
        peak_memory = torch.cuda.max_memory_allocated() / 1024**3  # GB
        
        forward_times.append(forward_time)
        memory_usage.append(peak_memory)
        
        if i % 5 == 0:
            print(f"   批次 {i+1}: {forward_time:.3f}s, 内存: {peak_memory:.2f}GB")
    
    avg_forward_time = np.mean(forward_times)
    std_forward_time = np.std(forward_times)
    avg_memory = np.mean(memory_usage)
    
    print(f"✅ 前向传播基准测试完成")
    print(f"   - 平均时间: {avg_forward_time:.3f}±{std_forward_time:.3f}s")
    print(f"   - 吞吐量: {B * T / avg_forward_time:.2f} clips/s")
    print(f"   - 平均显存: {avg_memory:.2f}GB")
    print(f"   - FPS (假设30fps视频): {B * T * 30 / avg_forward_time:.1f}")
    
    return avg_forward_time, avg_memory

def create_mock_instances(device='cpu', num_objects=5):
    """创建模拟的Instances对象"""
    from models.structures import Instances
    
    instances = Instances((800, 1333))
    instances.boxes = torch.randn(num_objects, 4, device=device)
    instances.labels = torch.zeros(num_objects, dtype=torch.long, device=device)
    instances.obj_ids = torch.arange(num_objects, dtype=torch.long, device=device)
    
    return instances

def analyze_parallel_efficiency():
    """分析并行化效率"""
    print("=" * 60)
    print("📈 并行化效率分析")
    print("=" * 60)
    
    args = create_benchmark_args()
    
    # 测试不同序列长度的性能
    sequence_lengths = [1, 2, 4, 8]
    results = {}
    
    for seq_len in sequence_lengths:
        print(f"\n🔍 测试序列长度: {seq_len}")
        
        args.sampler_lengths = [seq_len]
        model, _, _ = build_model(args)
        model = model.to(args.device)
        model.eval()
        
        # 创建测试数据
        B, T, C, H, W = 1, seq_len, 3, 800, 1333
        mock_data = {
            'imgs': torch.randn(B, T, C, H, W, device=args.device),
            'gt_instances': [[
                create_mock_instances(device=args.device) for _ in range(T)
            ] for _ in range(B)],
            'proposals': [[
                torch.randn(5, 5, device=args.device) for _ in range(T)
            ] for _ in range(B)]
        }
        
        # 预热
        with torch.no_grad():
            for _ in range(3):
                _ = model(mock_data)
        
        torch.cuda.synchronize()
        
        # 测试
        times = []
        for _ in range(10):
            start_time = time.time()
            with torch.no_grad():
                _ = model(mock_data)
            torch.cuda.synchronize()
            times.append(time.time() - start_time)
        
        avg_time = np.mean(times)
        per_frame_time = avg_time / seq_len
        
        results[seq_len] = {
            'total_time': avg_time,
            'per_frame_time': per_frame_time,
            'efficiency': 1.0 / per_frame_time if seq_len == 1 else (results[1]['per_frame_time'] / per_frame_time)
        }
        
        print(f"   - 总时间: {avg_time:.3f}s")
        print(f"   - 每帧时间: {per_frame_time:.3f}s")
        if seq_len > 1:
            print(f"   - 并行效率: {results[seq_len]['efficiency']:.2f}x")
    
    print(f"\n📊 并行化效率总结:")
    for seq_len, result in results.items():
        if seq_len > 1:
            print(f"   - 序列长度 {seq_len}: {result['efficiency']:.2f}x 加速")

def main():
    """主基准测试函数"""
    print("🚀 CIM-Tracker 并行化优化性能基准测试")
    print(f"🔧 PyTorch版本: {torch.__version__}")
    print(f"🎮 CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"🎮 GPU: {torch.cuda.get_device_name()}")
        print(f"💾 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    try:
        # 数据加载基准测试
        data_time = benchmark_data_loading()
        
        # 模型前向传播基准测试
        forward_time, memory = benchmark_model_forward()
        
        # 并行化效率分析
        analyze_parallel_efficiency()
        
        print("=" * 60)
        print("🎉 基准测试完成！")
        print("=" * 60)
        print(f"📊 关键指标:")
        print(f"   - 数据加载: {data_time:.3f}s/batch")
        print(f"   - 模型推理: {forward_time:.3f}s/batch")
        print(f"   - 显存使用: {memory:.2f}GB")
        print(f"   - 总体性能: {1/(data_time + forward_time):.2f} batches/s")
        
    except Exception as e:
        print(f"❌ 基准测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
