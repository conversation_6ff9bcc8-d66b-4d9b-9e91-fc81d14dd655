# CIM-Tracker 高性能训练配置文件
# 基于官方 mamba-ssm 的优化实现
# 专门为 WSL/Linux 环境设计

# ================== 基础模型配置 ==================
--meta_arch motr_mamba_ssm
--dataset_file e2e_dance
--epochs 20
--with_box_refine
--lr_drop 15
--lr_scheduler cosine
--lr 2e-4
--lr_backbone 2e-5

# ================== 预训练模型 ==================
# DanceTrack 专用预训练权重 (WSL路径)
--pretrained /mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# ================== 高性能配置 ==================
# 启用 mamba-ssm 高性能实现
--use_mamba_ssm

# 优化的批次大小 (利用 mamba-ssm 的高效性)
--batch_size 8
--sample_mode random_interval
--sample_interval 5
--sampler_lengths 10
--dropout 0.1

# ================== CIM-Tracker 特有参数 ==================
# Mamba 架构参数 (针对 mamba-ssm 优化)
--mamba_num_layers 4
--mamba_state_dim 16
--mamba_expand 2
--mamba_conv_dim 4
--id_dim 256

# ID 学习参数
--num_id_vocabulary 100
--id_loss_coef 2.0

# 轨迹增强参数
--aug_occlusion_prob 0.1
--aug_switch_prob 0.1
--mask_obs_threshold 0.5

# 高效训练参数
--grad_frames 5

# ================== 查询和检测参数 ==================
--num_queries 10
--query_denoise 0.1

# ================== 损失函数权重 ==================
--cls_loss_coef 2.0
--bbox_loss_coef 5.0
--giou_loss_coef 2.0

# ================== 数据集路径配置 ==================
# WSL 路径格式 (直接映射到Windows D盘)
--mot_path /mnt/d/Projects/Datasets
--det_db /mnt/d/Projects/Datasets/CIM_models/det_db_motrv2.json

# ================== 训练优化配置 ==================
# 启用所有性能优化
--use_checkpoint
--use_amp
--compile_model
--compile_mode max-autotune

# 多进程数据加载 (Linux 支持)
--num_workers 4

# ================== 输出配置 ==================
--output_dir outputs/cim_tracker_mamba_ssm
