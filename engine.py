# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-research. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from Deformable DETR (https://github.com/fundamentalvision/Deformable-DETR)
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------


"""
Train and eval functions used in main.py
"""
import math
import os
import sys
from typing import Iterable
import wandb # Add wandb import

import torch
from torch.amp import autocast, GradScaler
import util.misc as utils

from datasets.data_prefetcher import data_dict_to_cuda


def train_one_epoch_mot(model: torch.nn.Module, criterion: torch.nn.Module,
                    data_loader: Iterable, optimizer: torch.optim.Optimizer,
                    device: torch.device, epoch: int, max_norm: float = 0,
                    grad_frames: int = 5):  # 新增参数
    model.train()
    criterion.train()
    metric_logger = utils.MetricLogger(delimiter="  ")
    metric_logger.add_meter('lr', utils.SmoothedValue(window_size=1, fmt='{value:.6f}'))
    # metric_logger.add_meter('class_error', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    metric_logger.add_meter('grad_norm', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    header = 'Epoch: [{}]'.format(epoch)
    print_freq = 10

    # ================== 初始化 AMP GradScaler ==================
    # scaler = GradScaler()

    # for samples, targets in metric_logger.log_every(data_loader, print_freq, header):
    iteration = 0
    for data_dict in metric_logger.log_every(data_loader, print_freq, header):
        data_dict = data_dict_to_cuda(data_dict, device)

        # ================== AMP 前向传播 ==================
        # with autocast(device_type='cuda'):
        outputs = model(data_dict)
        loss_dict = criterion(outputs, data_dict['gt_instances'])
        # print("iter {} after model".format(cnt-1))
        weight_dict = criterion.weight_dict

        # ================== 高效长序列训练逻辑 ==================
        # 仅对最后 'grad_frames' 帧的损失进行反向传播
        total_frames = len(data_dict['imgs'])
        start_grad_frame = max(0, total_frames - grad_frames)

        # 收集需要反向传播的损失
        grad_losses = []
        for k, v in loss_dict.items():
            if k in weight_dict:
                # 从损失名称中解析帧索引, e.g., 'frame_5_loss_id' -> 5
                try:
                    frame_idx = int(k.split('_')[1])
                    if frame_idx >= start_grad_frame:
                        grad_losses.append(v * weight_dict[k])
                except (ValueError, IndexError):
                    # 如果损失名称不符合'frame_x_...'格式，默认全部计算梯度
                    grad_losses.append(v * weight_dict[k])

        # 计算总的梯度损失
        if grad_losses:
            losses = sum(grad_losses)
        else:
            # 如果没有需要梯度的损失，创建一个零张量
            losses = torch.tensor(0.0, device=next(iter(loss_dict.values())).device if loss_dict else 'cpu', requires_grad=True)

        # 为了日志记录，我们仍然计算所有帧的总损失值
        valid_losses = [loss_dict[k] * weight_dict[k] for k in loss_dict.keys() if k in weight_dict]
        if valid_losses:
            full_losses_reduced = sum(valid_losses)
        else:
            # 如果没有有效损失，创建一个零张量
            full_losses_reduced = torch.tensor(0.0, device=next(iter(loss_dict.values())).device if loss_dict else 'cpu')
        # ======================================================

        # reduce losses over all GPUs for logging purposes
        loss_dict_reduced = utils.reduce_dict(loss_dict)
        # loss_dict_reduced_unscaled = {f'{k}_unscaled': v
        #                               for k, v in loss_dict_reduced.items()}
        loss_dict_reduced_scaled = {k: v * weight_dict[k]
                                    for k, v in loss_dict_reduced.items() if k in weight_dict}
        losses_reduced_scaled = sum(loss_dict_reduced_scaled.values())

        loss_value = full_losses_reduced.item()  # 日志记录完整损失

        if not math.isfinite(loss_value):
            print("Loss is {}, stopping training".format(loss_value))
            print(loss_dict_reduced)
            sys.exit(1)

        optimizer.zero_grad()

        # ================== AMP 反向传播 ==================
        # scaler.scale(losses).backward()  # 只反向传播部分损失
        losses.backward()

        if max_norm > 0:
            # AMP 梯度裁剪需要先 unscale
            # scaler.unscale_(optimizer)
            grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
        else:
            # scaler.unscale_(optimizer)
            grad_total_norm = utils.get_total_grad_norm(model.parameters(), max_norm)

        # AMP 优化器步骤
        # scaler.step(optimizer)
        # scaler.update()
        optimizer.step()

        # metric_logger.update(loss=loss_value, **loss_dict_reduced_scaled, **loss_dict_reduced_unscaled)
        metric_logger.update(loss=loss_value, **loss_dict_reduced_scaled)
        # metric_logger.update(class_error=loss_dict_reduced['class_error'])
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])
        metric_logger.update(grad_norm=grad_total_norm)
        
        # Log metrics to WandB
        if utils.is_main_process():
            log_data = {
                'train_loss': loss_value,
                'lr': optimizer.param_groups[0]["lr"],
                'grad_norm': grad_total_norm.item() if isinstance(grad_total_norm, torch.Tensor) else grad_total_norm,
                **{f'train_{k}': v.item() if isinstance(v, torch.Tensor) else v for k, v in loss_dict_reduced_scaled.items()}
            }
            wandb.log(log_data, step=epoch * len(data_loader) + iteration)

        iteration += 1


    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger)
    
    # Log epoch-level averaged stats to WandB
    if utils.is_main_process():
        epoch_log_data = {f'epoch_avg_train_{k}': meter.global_avg for k, meter in metric_logger.meters.items() if k not in ['lr', 'grad_norm']}
        epoch_log_data['epoch'] = epoch
        wandb.log(epoch_log_data)
        
    return {k: meter.global_avg for k, meter in metric_logger.meters.items()}
