# CIM-Tracker SPORTSMOT 配置文件
# 自动生成，适配 WINDOWS 环境
# 生成时间: auto-generated

# ================== 基础模型配置 ==================
--meta_arch motr
--dataset_file e2e_joint
--epochs 20
--with_box_refine
--lr_drop 15
--lr_scheduler cosine
--lr 2e-4
--lr_backbone 2e-5

# ================== 预训练模型 ==================
--pretrained D:/Projects/Datasets/CIM_models\r50_deformable_detr_coco_sportsmot.pth

# ================== 标准配置 ==================
--batch_size 4
--sampler_lengths 5
--sample_mode random_interval
--sample_interval 5
--dropout 0.1

# ================== CIM-Tracker 特有参数 ==================
--mamba_num_layers 4
--mamba_state_dim 16
--mamba_expand 2
--mamba_conv_dim 4
--id_dim 256

# ================== ID 学习参数 ==================
--num_id_vocabulary 100
--id_loss_coef 2.0

# ================== 轨迹增强参数 ==================
--aug_occlusion_prob 0.1
--aug_switch_prob 0.1
--mask_obs_threshold 0.5

# ================== 高效训练参数 ==================
--grad_frames 3

# ================== 查询和检测参数 ==================
--num_queries 30
--query_denoise 0.1

# ================== 损失函数权重 ==================
--cls_loss_coef 2.0
--bbox_loss_coef 5.0
--giou_loss_coef 2.0

# ================== 数据集路径配置 ==================
--mot_path D:/Projects/Datasets
--det_db D:/Projects/Datasets/CIM_models\det_db_motrv2.json

# ================== 训练优化配置 ==================
--use_checkpoint
--use_amp
# --compile_model  # Windows 兼容性问题
# --compile_mode reduce-overhead
--num_workers 0

# ================== 输出配置 ==================
--output_dir outputs/cim_tracker_sportsmot